<script setup>
import <PERSON> from 'papa<PERSON><PERSON>';
import { v4 as uuidv4 } from 'uuid';
import { computed, onMounted, reactive } from 'vue';
import { useBiStore } from '~/bi/store/bi.store';
import BiHandsontable from './bi-handsontable.vue';

const props = defineProps({
  id: {
    type: String,
    default: () => uuidv4(),
  },
  isPivotTable: {
    type: Boolean,
    default: false,
  },
});

const bi_store = useBiStore();

const state = reactive({
  loading: false,
  error: null,
  csv_data: [],
  columns: [],

  table_columns: [],
  table_data: [],
  nested_headers: [],
  nested_rows: false,

  table_instance: null,
});

const table_settings = computed(() => {
  if (props.isPivotTable) {
    const rows = bi_store.table_preview_config.rows?.length || 0;

    const rowHeaderWidth = Math.min(
      150,
      35 + Array.from({ length: rows }).reduce((sum, _, i) => sum + 40 * 0.8 ** i, 0),
    );
    return {
      rowHeaderWidth,
    };
  }
  return {};
});

// -------------------------------- Methods --------------------------------- //
function getTableColumn(col) {
  if (typeof col === 'string') {
    return {
      label: col,
      data: col,
      type: 'text',
      readOnly: true,
      width: 150,
    };
  }
  return {
    label: col.label,
    data: col.key || col.label,
    type: 'text',
    readOnly: true,
    width: 150,
  };
}
async function fetchProgressHistoryData() {
  try {
    state.loading = true;
    state.error = null;

    // Fetch the CSV file from the public directory
    const csvPath = '/progress-history.csv';

    // For demo purposes, we'll simulate fetching from the actual file
    // In a real implementation, you might fetch from an API endpoint
    const response = await fetch(csvPath);

    if (!response.ok) {
      throw new Error(`Failed to fetch CSV: ${response.statusText}`);
    }

    const csvText = await response.text();

    // Parse CSV using Papa Parse
    const parseResult = Papa.parse(csvText, {
      header: true,
      skipEmptyLines: true,
      transformHeader: header => header.trim(),
    });

    if (parseResult.errors.length > 0) {
      console.warn('CSV parsing warnings:', parseResult.errors);
    }

    // Extract columns from the first row (headers)
    state.columns = parseResult.meta.fields || [];
    bi_store.table_config.columns = state.columns;
    // Process the data
    state.csv_data = parseResult.data.slice(0, 1000);

    if (props.isPivotTable) {
      await configureChartOptions();
    }
    else {
      state.table_columns = state.columns.map(col => getTableColumn(col));
      state.table_data = state.csv_data;
    }
  }
  catch (error) {
    console.error('Error fetching progress history data:', error);
    state.error = error.message;
  }
  finally {
    state.loading = false;
  }
}

function splitKeysByIsNumeric(data) {
  const value_keys = [];
  const non_value_keys = [];

  const allKeys = new Set();
  data.forEach(obj => Object.keys(obj).forEach(key => allKeys.add(key)));

  for (const key of allKeys) {
    let is_numeric = true;

    for (const obj of data) {
      const value = obj[key];

      if (
        value === '' // exclude empty string
        || Number.isNaN(Number(value)) // check if value is not a number
        || value === null // null is non-numeric here
        || typeof value === 'boolean' // ignore booleans
        || typeof value === 'object' // exclude arrays and objects
      ) {
        is_numeric = false;
        break;
      }
    }

    if (is_numeric) {
      value_keys.push(key);
    }
    else {
      non_value_keys.push(key);
    }
  }

  return { value_keys, non_value_keys };
}

async function configureChartOptions(updateTable = false) {
  // Apply any data formatting and grouping
  if (!updateTable) {
    const result = splitKeysByIsNumeric(state.csv_data);
    const keys = result.non_value_keys.filter(key => key !== 'ID').slice(); // clone the array to avoid mutation
    const half = Math.ceil(keys.length / 2);
    bi_store.table_preview_config.rows = keys.slice(0, half);
    bi_store.table_preview_config.columns = keys.slice(half);
    bi_store.table_preview_config.values = result.value_keys;
  }

  const nested_headers = await generateNestedTableStructure(
    state.csv_data,
    bi_store.table_preview_config.columns,
    bi_store.table_preview_config.values,
    bi_store.table_preview_config.rows,
  );

  if (bi_store.table_preview_config.rows?.length)
    state.nested_rows = true;

  state.table_columns = nested_headers.slice(-1)[0].map(col => getTableColumn(col));
  state.nested_headers = nested_headers;
  state.table_data = nested_data;

  if (updateTable) {
    state.table_instance?.updateTableSettings();
  }
}

async function generateNestedTable(data, columns, values, rowHeaders = [], delimiter = '|') {
  const nestedHeaders = [];

  // Handle case: no column pivoting
  if (columns.length === 0) {
    const flatHeaderRow = [];

    // Add row headers
    for (const rh of rowHeaders) {
      flatHeaderRow.push({ label: rh });
    }

    // Add values directly as columns
    for (const val of values) {
      flatHeaderRow.push({ label: val, key: val });
    }

    nestedHeaders.push(flatHeaderRow);

    // Generate flat data structure
    const flatData = rowHeaders.length === 0
      ? [createFlatRow(data, values, [])]
      : groupByLevel(data, rowHeaders, values, []);

    return { headers: nestedHeaders, data: flatData };
  }

  // Build tree structure for column pivoting
  function buildTree(data, level = 0) {
    if (level >= columns.length)
      return [];

    const groups = {};
    for (const row of data) {
      const key = row[columns[level]];
      if (!groups[key])
        groups[key] = [];
      groups[key].push(row);
    }

    const nodes = [];
    for (const key in groups) {
      const children = buildTree(groups[key], level + 1);
      nodes.push({ label: key, children });
    }
    return nodes;
  }

  const tree = buildTree(data);

  function countLeaves(node) {
    if (!node.children || node.children.length === 0)
      return 1;
    return node.children.reduce((sum, child) => sum + countLeaves(child), 0);
  }

  // Generate nested headers
  function fillHeaders(nodes, level = 0) {
    if (!nestedHeaders[level]) {
      nestedHeaders[level] = [];
      for (let i = 0; i < rowHeaders.length; i++) {
        nestedHeaders[level].push({ label: '', colspan: 1 });
      }
    }

    for (const node of nodes) {
      const leafCount = countLeaves(node);
      nestedHeaders[level].push({
        label: node.label,
        colspan: leafCount * values.length,
      });

      if (node.children.length > 0) {
        fillHeaders(node.children, level + 1);
      }
    }
  }

  fillHeaders(tree);

  const finalRow = [];
  for (const rh of rowHeaders) {
    finalRow.push({ label: rh, key: rh });
  }

  function pushLeafLabels(nodes, path = []) {
    for (const node of nodes) {
      const currentPath = [...path, node.label];
      if (node.children.length > 0) {
        pushLeafLabels(node.children, currentPath);
      }
      else {
        for (const val of values) {
          finalRow.push({
            label: val,
            key: currentPath.concat(val).join(delimiter),
          });
        }
      }
    }
  }

  pushLeafLabels(tree);
  nestedHeaders.push(finalRow);

  return nestedHeaders;
}

async function generateNestedTableData(data, columns, values, rowHeaders = [], delimiter = '|') {

}

function getCellFormatting({ value, column }) {
  const col = column.label;
  // Date column formatting
  if (col === 'Date') {
    const date = new Date(value);
    const today = new Date();
    return {
      'background-color': date < today ? '#FFEBEE' : null,
      'color': date < today ? '#D32F2F' : null,
    };
  }

  // Value column formatting (progress value)
  if (col === 'Value') {
    const val = Number.parseFloat(value);
    if (!Number.isNaN(val)) {
      return {
        'text-align': 'right',
        'color': val === 1 ? '#0F9D58' : '#DB4437',
        'font-weight': '600',
      };
    }
  }

  // Reset column formatting
  if (col === 'Reset') {
    return {
      'background-color': value === 'True' ? '#FFF3E0' : null,
      'color': value === 'True' ? '#E65100' : null,
      'font-style': value === 'True' ? 'italic' : null,
    };
  }

  // User column formatting
  if (col === 'User') {
    return {
      color: '#1976D2',
    };
  }

  // Default formatting
  return null;
}

// -------------------------------- Lifecycle ------------------------------- //
onMounted(() => {
  fetchProgressHistoryData();
});

watch(() => ({ ...bi_store.table_preview_config }), () => {
  if (state.loading || !state.table_instance)
    return;
  configureChartOptions(true);
});
</script>

<template>
  <div class="h-[100%] w-full">
    <BiHandsontable
      v-if="!state.loading && !state.error && state.table_data.length > 0"
      :bi-table-id="`preview-table-${props.id}`"
      :data="state.table_data"
      :columns="state.table_columns"
      :column-config="state.column_config"
      :nested-headers="state.nested_headers"
      :nested-rows="state.nested_rows"
      :show-skeleton-loader="state.loading"
      :get-cell-formatting="getCellFormatting"
      :row-headers="true"
      :additional-table-settings="table_settings"
      class="h-full"
      :enable-column-sorting="!props.isPivotTable"
      @table-instance="state.table_instance = $event"
    />
  </div>
</template>

<style scoped lang="scss">
// Add any custom styles here
</style>
